import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// POST /api/loading-charts/receive
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.chart_id || !body.lr_entries || body.lr_entries.length === 0) {
      return NextResponse.json(
        { error: "Missing required fields: chart_id, lr_entries" },
        { status: 400 }
      );
    }

    // Get the loading chart
    const { data: chart, error: chartError } = await supabase
      .from("loading_charts")
      .select("*")
      .eq("chart_id", body.chart_id)
      .single();

    if (chartError || !chart) {
      console.error("Error fetching loading chart:", chartError);
      return NextResponse.json({ error: "Loading chart not found" }, {
        status: 404,
      });
    }

    // Get the user ID
    const { data: { user } } = await routeHandlerClient.auth.getUser();
    
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 401 });
    }

    // Process each LR entry
    const results: any[] = [];
    for (const entry of body.lr_entries) {
      // Validate entry
      if (!entry.lr_number || !entry.received_quantity) {
        results.push({
          lr_number: entry.lr_number,
          success: false,
          error: "Missing required fields: lr_number, received_quantity",
        });
        continue;
      }

      // Get the loading chart item
      const { data: item, error: itemError } = await supabase
        .from("loading_chart_items")
        .select("*")
        .eq("chart_id", body.chart_id)
        .eq("lr_number", entry.lr_number)
        .single();

      if (itemError || !item) {
        results.push({
          lr_number: entry.lr_number,
          success: false,
          error: "Loading chart item not found",
        });
        continue;
      }

      // Update the loading chart item
      const { error: updateError } = await supabase
        .from("loading_chart_items")
        .update({
          status: "Received",
          received_by: user.id,
          received_quantity: entry.received_quantity,
          received_at: new Date().toISOString(),
        })
        .eq("chart_id", body.chart_id)
        .eq("lr_number", entry.lr_number);

      if (updateError) {
        console.error(`Error updating loading chart item ${entry.lr_number}:`, updateError);
        results.push({
          lr_number: entry.lr_number,
          success: false,
          error: "Failed to update loading chart item",
        });
        continue;
      }

      results.push({
        lr_number: entry.lr_number,
        success: true,
      });
    }

    // Check if all items in the loading chart have been received
    const { data: pendingItems, error: pendingError } = await supabase
      .from("loading_chart_items")
      .select("*")
      .eq("chart_id", body.chart_id)
      .eq("status", "Pending");

    if (!pendingError && (!pendingItems || pendingItems.length === 0)) {
      // All items have been received, update the loading chart status
      await supabase
        .from("loading_charts")
        .update({ status: "Received" })
        .eq("chart_id", body.chart_id);
    }

    return NextResponse.json({
      message: "Parcels received successfully",
      results,
    });
  } catch (error: any) {
    console.error("Error in POST /api/loading-charts/receive:", error);
    return NextResponse.json({ error: "Internal Server Error" }, {
      status: 500,
    });
  }
}
