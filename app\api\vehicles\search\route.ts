import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/vehicles/search
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const url = new URL(request.url);
    const registration_number = url.searchParams.get("registration_number");

    if (!registration_number) {
      return NextResponse.json(
        { error: "Missing required parameter: registration_number" },
        { status: 400 }
      );
    }

    // Search for vehicle by registration number
    const { data: vehicle, error: vehicleError } = await supabase
      .from("vehicles")
      .select(`
        vehicle_id,
        registration_number,
        vehicle_type,
        make_model,
        capacity,
        current_status,
        branch_id,
        branch:branches(name, code)
      `)
      .eq("registration_number", registration_number.toUpperCase())
      .single();

    if (vehicleError || !vehicle) {
      return NextResponse.json(
        { error: "Vehicle not found" },
        { status: 404 }
      );
    }

    // Get user's branch to validate access
    const { data: { user } } = await routeHandlerClient.auth.getUser();
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id, role")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has access to this vehicle
    // Users can only access vehicles from their own branch unless they are Super Admin
    if (userData.role !== 'Super Admin' && vehicle.branch_id !== userData.branch_id) {
      return NextResponse.json(
        { error: "You don't have access to this vehicle" },
        { status: 403 }
      );
    }

    // Check if vehicle has any active loading charts
    const { data: activeCharts, error: chartsError } = await supabase
      .from("loading_charts")
      .select("chart_id, chart_number, status, created_at")
      .eq("vehicle_id", vehicle.vehicle_id)
      .in("status", ["Created", "In Progress"])
      .order("created_at", { ascending: false });

    if (chartsError) {
      console.error("Error checking active loading charts:", chartsError);
      // Don't fail the request, just log the error
    }

    return NextResponse.json({
      vehicle: {
        ...vehicle,
        active_loading_charts: activeCharts || []
      }
    });

  } catch (error: any) {
    console.error("Error in GET /api/vehicles/search:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
