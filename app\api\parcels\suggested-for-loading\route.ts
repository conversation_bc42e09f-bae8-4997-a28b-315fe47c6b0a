import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// POST /api/parcels/suggested-for-loading
export async function POST(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.destination_branch_id || !body.loading_type) {
      return NextResponse.json(
        { error: "Missing required fields: destination_branch_id, loading_type" },
        { status: 400 }
      );
    }

    // Validate loading type
    if (!['Direct', 'Via'].includes(body.loading_type)) {
      return NextResponse.json(
        { error: "Invalid loading_type. Must be 'Direct' or 'Via'" },
        { status: 400 }
      );
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Build query based on loading type
    let query = supabase
      .from("parcels")
      .select(`
        parcel_id,
        lr_number,
        sender_name,
        recipient_name,
        delivery_branch_id,
        number_of_items,
        weight,
        current_status,
        booking_datetime,
        delivery_branch:branches!delivery_branch_id(name, code)
      `)
      .eq("current_status", "Booked")
      .eq("sender_branch_id", userData.branch_id)
      .order("booking_datetime", { ascending: true });

    // Apply destination filter based on loading type
    if (body.loading_type === 'Direct') {
      // Direct loading: only parcels whose final destination matches the loading destination
      query = query.eq("delivery_branch_id", body.destination_branch_id);
    } else {
      // Via loading: only parcels whose final destination does NOT match the loading destination
      query = query.neq("delivery_branch_id", body.destination_branch_id);
    }

    const { data: parcels, error: parcelsError } = await query.limit(50); // Limit to 50 suggestions

    if (parcelsError) {
      console.error("Error fetching suggested parcels:", parcelsError);
      return NextResponse.json({ error: "Failed to fetch suggested parcels" }, { status: 500 });
    }

    // Filter out parcels that are already in pending loading charts
    if (parcels && parcels.length > 0) {
      const lrNumbers = parcels.map(p => p.lr_number);
      
      const { data: existingItems, error: existingError } = await supabase
        .from("loading_chart_items")
        .select("lr_number")
        .in("lr_number", lrNumbers)
        .eq("status", "Pending");

      if (existingError) {
        console.error("Error checking existing loading items:", existingError);
        return NextResponse.json({ error: "Failed to check existing loading items" }, { status: 500 });
      }

      const existingLRs = existingItems?.map(item => item.lr_number) || [];
      const availableParcels = parcels.filter(parcel => !existingLRs.includes(parcel.lr_number));

      return NextResponse.json({
        parcels: availableParcels,
        loading_type: body.loading_type,
        destination_branch_id: body.destination_branch_id,
        total_available: availableParcels.length
      });
    }

    return NextResponse.json({
      parcels: [],
      loading_type: body.loading_type,
      destination_branch_id: body.destination_branch_id,
      total_available: 0
    });

  } catch (error: any) {
    console.error("Error in POST /api/parcels/suggested-for-loading:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
