"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Truck, Search, AlertCircle, PackageOpen, Check, Plus, Minus, Share } from "lucide-react"
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { getUserBranchId, getUserBranchIdAsync } from "@/lib/branch-utils"
import { supabase } from "@/lib/supabase"

interface VehicleMemo {
  memo_id: number
  memo_number: string
  from_branch_id: number
  to_branch_id: number
  vehicle_id: number
  status: string
  from_branch: {
    name: string
    code: string
  }
  to_branch: {
    name: string
    code: string
  }
  vehicle: {
    registration_number: string
    vehicle_type: string
  }
}

interface LoadingChart {
  chart_id: number
  chart_number: string
  memo_id: number
  vehicle_id: number
  destination_branch_id: number
  status: string
  items: any[]
}

interface ParcelToReceive {
  parcel_id: number
  lr_number: string
  sender_name: string
  recipient_name: string
  number_of_items: number
  current_status: string
  chart_id: number
  item_id: number
  loaded_quantity: number
  sender_branch: {
    name: string
    code: string
  }
  delivery_branch: {
    name: string
    code: string
  }
  received_quantity?: number
}

export function VehicleReceivingPanel() {
  const { toast } = useToast()
  const [vehicleNumber, setVehicleNumber] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userBranchId, setUserBranchId] = useState<string | null>(null)
  const [step, setStep] = useState<"search" | "lr" | "confirmation" | "complete">("search")

  // Data states
  const [memo, setMemo] = useState<VehicleMemo | null>(null)
  const [loadingCharts, setLoadingCharts] = useState<LoadingChart[]>([])
  const [parcelsToReceive, setParcelsToReceive] = useState<ParcelToReceive[]>([])
  const [selectedParcels, setSelectedParcels] = useState<ParcelToReceive[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [receiptNumber, setReceiptNumber] = useState<string | null>(null)

  // LR input states
  const [currentLR, setCurrentLR] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)

  // Initialize branch ID from API
  useEffect(() => {
    // Define an async function inside the effect
    const fetchBranchId = async () => {
      try {
        // First try the async version which is more reliable
        const branchIdValue = await getUserBranchIdAsync();

        // Check if branchId is null or undefined before calling toString()
        const branchId = branchIdValue !== null && branchIdValue !== undefined
          ? branchIdValue.toString()
          : null;

        setUserBranchId(branchId);
      } catch (error: any) {
        // If async version fails, fall back to sync version with null check
        console.error("Error fetching branch ID:", error);
        const fallbackId = getUserBranchId();
        if (fallbackId !== null && fallbackId !== undefined) {
          setUserBranchId(fallbackId.toString());
        }
      }
    };

    // Call the async function
    fetchBranchId();
  }, []);

  // Search for vehicle memo
  const searchVehicle = async () => {
    if (!vehicleNumber.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter a vehicle number",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)
    setError(null)

    try {
      // Build URL with user's branch ID
      const url = new URL('/api/memos/vehicle/receive', window.location.origin)
      url.searchParams.append('registration_number', vehicleNumber.trim())

      // Use the branch ID from state, or try to get it again if not available
      if (userBranchId) {
        url.searchParams.append('user_branch_id', userBranchId)
      } else {
        // If we don't have a branch ID yet, try to get it again
        try {
          const branchId = await getUserBranchIdAsync();
          if (branchId !== null) {
            const branchIdStr = branchId.toString();
            url.searchParams.append('user_branch_id', branchIdStr);
            // Update state for future use
            setUserBranchId(branchIdStr);
          } else {
            throw new Error("Could not determine your branch. Please try again or contact support.");
          }
        } catch (branchError: any) {
          console.error('Error getting branch ID:', branchError);
          setError('Could not determine your branch. Please try again or contact support.');
          setIsSearching(false);
          return;
        }
      }

      const response = await fetch(url.toString())
      const data = await response.json()

      if (response.ok && data.available) {
        setMemo(data.memo)
        setLoadingCharts(data.loadingCharts || [])
        // Don't set parcelsToReceive yet - we'll fetch them when the user enters an LR number
        setStep("lr") // Go to LR input step instead of parcels
      } else {
        setError(data.error || "No parcels to receive from this vehicle")
      }
    } catch (error: any) {
      console.error('Error searching vehicle:', error)
      setError('Failed to search vehicle. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  // Handle key press in vehicle input
  const handleVehicleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      searchVehicle()
    }
  }

  // Handle key press in LR input
  const handleLRKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      validateAndFetchLR()
    }
  }

  // Validate and fetch LR details
  const validateAndFetchLR = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    if (!memo || !userBranchId) {
      setValidationError("Vehicle information is missing. Please try again.")
      return
    }

    setIsValidating(true)
    setValidationError(null)

    try {
      // Build query parameters
      const params = new URLSearchParams({
        lr_number: currentLR.trim(),
        memo_id: memo.memo_id.toString()
      })

      if (userBranchId) {
        params.append('user_branch_id', userBranchId)
      }

      const response = await fetch(`/api/parcels/validate-lr/receive?${params.toString()}`)
      const data = await response.json()

      if (response.ok && data.valid) {
        // Check if this LR is already in the list
        const isAlreadyAdded = parcelsToReceive.some(p => p.lr_number === data.parcel.lr_number);

        if (isAlreadyAdded) {
          setValidationError(`LR number ${data.parcel.lr_number} is already added to the list`);
        } else {
          // Add the parcel to the list of parcels to receive with received quantity initialized to 0
          const newParcel = {
            ...data.parcel,
            received_quantity: 0 // Initialize to 0 so user must explicitly set the quantity
          };

          setParcelsToReceive(prev => [...prev, newParcel]);

          // Don't add to selected parcels yet - user needs to set quantity first

          // Clear the LR input
          setCurrentLR("");

          toast({
            title: "Parcel Found",
            description: `LR ${data.parcel.lr_number} found. Please set the received quantity.`,
          });
        }
      } else {
        setValidationError(data.message || "Invalid LR number or LR not found for this vehicle")
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      setValidationError('Failed to validate LR. Please try again.')
    } finally {
      setIsValidating(false)
    }
  }

  // Handle selecting a parcel for receiving
  const handleParcelSelect = (parcel: ParcelToReceive) => {
    // Check if parcel is already selected
    const isSelected = selectedParcels.some(p => p.lr_number === parcel.lr_number)

    if (isSelected) {
      // Remove from selected parcels
      setSelectedParcels(prev => prev.filter(p => p.lr_number !== parcel.lr_number))
    } else {
      // Add to selected parcels with default received quantity
      setSelectedParcels(prev => [...prev, {
        ...parcel,
        received_quantity: parcel.loaded_quantity
      }])
    }
  }

  // Handle changing received quantity
  const handleQuantityChange = (lrNumber: string, change: number) => {
    setSelectedParcels(prev => prev.map(parcel => {
      if (parcel.lr_number === lrNumber) {
        const newQuantity = Math.max(1, Math.min(parcel.loaded_quantity, (parcel.received_quantity || 0) + change))
        return {
          ...parcel,
          received_quantity: newQuantity
        }
      }
      return parcel
    }))
  }

  // Handle proceeding to confirmation
  const handleProceedToConfirmation = () => {
    if (selectedParcels.length === 0) {
      toast({
        title: "No Parcels Selected",
        description: "Please select at least one parcel to receive",
        variant: "destructive",
      })
      return
    }

    setStep("confirmation")
  }

  // Check if all parcels have been received
  const checkAllParcelsReceived = async () => {
    if (!memo || !loadingCharts || loadingCharts.length === 0) {
      return { complete: false, totalParcels: 0, receivedParcels: 0 };
    }

    try {
      // Get the total count of parcels in all loading charts
      let totalParcels = 0;
      let pendingItems: any[] = [];

      for (const chart of loadingCharts) {
        const { data: items, error } = await supabase
          .from("loading_chart_items")
          .select("*")
          .eq("chart_id", chart.chart_id)
          .eq("status", "Pending");

        if (error) {
          console.error("Error checking pending items:", error);
          continue;
        }

        if (items) {
          totalParcels += items.length;
          pendingItems = [...pendingItems, ...items];
        }
      }

      // Calculate how many parcels we're receiving now
      const receivedParcels = selectedParcels.length;

      // Calculate how many will remain pending after this operation
      const remainingParcels = totalParcels - receivedParcels;

      return {
        complete: remainingParcels === 0,
        totalParcels,
        receivedParcels,
        remainingParcels
      };
    } catch (error: any) {
      console.error("Error checking all parcels:", error);
      return { complete: false, totalParcels: 0, receivedParcels: 0 };
    }
  };

  // Handle submitting the received parcels
  const handleSubmitReceived = async () => {
    if (selectedParcels.length === 0) {
      toast({
        title: "No Parcels Selected",
        description: "Please select at least one parcel to receive",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Check if all parcels are being received
      const parcelStatus = await checkAllParcelsReceived();

      // Group parcels by chart_id
      const parcelsByChart: Record<number, any[]> = {}

      selectedParcels.forEach(parcel => {
        if (!parcelsByChart[parcel.chart_id]) {
          parcelsByChart[parcel.chart_id] = []
        }

        parcelsByChart[parcel.chart_id].push({
          lr_number: parcel.lr_number,
          received_quantity: parcel.received_quantity
        })
      })

      // Submit each chart separately
      const results = []

      for (const [chartId, lrEntries] of Object.entries(parcelsByChart)) {
        const response = await fetch('/api/loading-charts/receive', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            chart_id: parseInt(chartId),
            lr_entries: lrEntries
          }),
        })

        const data = await response.json()

        if (response.ok) {
          results.push({
            chart_id: chartId,
            success: true,
            message: data.message
          })
        } else {
          results.push({
            chart_id: chartId,
            success: false,
            error: data.error
          })
        }
      }

      // Check if all submissions were successful
      const allSuccessful = results.every(result => result.success)

      if (allSuccessful) {
        // Generate a receipt number
        const date = new Date()
        const year = date.getFullYear().toString().slice(-2)
        const month = (date.getMonth() + 1).toString().padStart(2, "0")
        const day = date.getDate().toString().padStart(2, "0")
        const random = Math.floor(Math.random() * 10000).toString().padStart(4, "0")
        const receiptNum = `RCV${year}${month}${day}${random}`

        setReceiptNumber(receiptNum)
        setStep("complete")

        // Show different toast messages based on whether all parcels were received
        if (parcelStatus.complete) {
          toast({
            title: "All Parcels Received",
            description: `Successfully received all ${selectedParcels.length} parcels for this vehicle`,
          })
        } else {
          toast({
            title: "Parcels Received",
            description: `Received ${selectedParcels.length} of ${parcelStatus.totalParcels} parcels. ${parcelStatus.remainingParcels} parcels remain to be received.`,
          })
        }
      } else {
        // Some submissions failed
        const errors = results.filter(result => !result.success).map(result => result.error)
        setError(`Failed to receive some parcels: ${errors.join(", ")}`)

        toast({
          title: "Error",
          description: "Failed to receive some parcels. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error receiving parcels:', error)
      setError('Failed to receive parcels. Please try again.')

      toast({
        title: "Error",
        description: "Failed to receive parcels. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset the form
  const handleReset = () => {
    setVehicleNumber("")
    setMemo(null)
    setLoadingCharts([])
    setParcelsToReceive([])
    setSelectedParcels([])
    setReceiptNumber(null)
    setError(null)
    setStep("search")
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {step === "search" && (
        <Card className="w-full max-w-md mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Truck className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Receive Parcels</h2>
              <p className="text-muted-foreground">
                Enter the vehicle number to receive parcels
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="vehicle-number">Vehicle Number</Label>
                <div className="flex space-x-2">
                  <Input
                    id="vehicle-number"
                    placeholder="Enter vehicle registration number"
                    value={vehicleNumber}
                    onChange={(e) => setVehicleNumber(e.target.value)}
                    onKeyDown={handleVehicleKeyPress}
                    disabled={isSearching}
                  />
                  <Button onClick={searchVehicle} disabled={isSearching || !vehicleNumber.trim()}>
                    {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {step === "lr" && memo && (
        <Card className="w-full max-w-2xl mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <PackageOpen className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Receive Parcels</h2>
              <p className="text-muted-foreground">
                Enter the LR number to receive parcels from this vehicle
              </p>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2">Vehicle Details:</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm">
                      <span className="font-medium">Vehicle:</span> {memo.vehicle.registration_number} ({memo.vehicle.vehicle_type})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Memo:</span> {memo.memo_number}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm">
                      <span className="font-medium">From:</span> {memo.from_branch.name} ({memo.from_branch.code})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">To:</span> {memo.to_branch.name} ({memo.to_branch.code})
                    </p>
                  </div>
                </div>
              </div>

              {validationError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{validationError}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="lr-number">LR Number</Label>
                <div className="flex space-x-2">
                  <Input
                    id="lr-number"
                    placeholder="Enter LR number"
                    value={currentLR}
                    onChange={(e) => setCurrentLR(e.target.value)}
                    onKeyDown={handleLRKeyPress}
                    disabled={isValidating}
                  />
                  <Button onClick={validateAndFetchLR} disabled={isValidating || !currentLR.trim()}>
                    {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {/* Current parcel being processed */}
              {parcelsToReceive.length > 0 && parcelsToReceive.some(p => !selectedParcels.some(sp => sp.lr_number === p.lr_number)) && (
                <div className="space-y-4 p-4 border rounded-md">
                  <h3 className="font-medium">Current Parcel</h3>
                  {parcelsToReceive
                    .filter(p => !selectedParcels.some(sp => sp.lr_number === p.lr_number))
                    .slice(0, 1)
                    .map(parcel => (
                      <div key={parcel.lr_number} className="space-y-4">
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-lg">{parcel.lr_number}</span>
                            <Badge variant="outline">
                              {parcel.loaded_quantity} items
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <p>
                              <span className="font-medium">From:</span> {parcel.sender_branch?.name}
                            </p>
                            <p>
                              <span className="font-medium">To:</span> {parcel.delivery_branch?.name}
                            </p>
                            <p>
                              <span className="font-medium">Sender:</span> {parcel.sender_name}
                            </p>
                            <p>
                              <span className="font-medium">Recipient:</span> {parcel.recipient_name}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label>Items Received</Label>
                            <span className="text-sm font-medium">
                              {parcel.received_quantity || 0} of {parcel.loaded_quantity}
                            </span>
                          </div>

                          <div className="h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="h-full bg-primary"
                              style={{
                                width: `${((parcel.received_quantity || 0) / parcel.loaded_quantity) * 100}%`
                              }}
                            ></div>
                          </div>

                          <div className="flex items-center justify-between mt-2">
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Set received quantity to 0
                                  setParcelsToReceive(prev => prev.map(p =>
                                    p.lr_number === parcel.lr_number
                                      ? { ...p, received_quantity: 0 }
                                      : p
                                  ));
                                }}
                                disabled={(parcel.received_quantity || 0) === 0}
                              >
                                Reset
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Set received quantity to max
                                  setParcelsToReceive(prev => prev.map(p =>
                                    p.lr_number === parcel.lr_number
                                      ? { ...p, received_quantity: p.loaded_quantity }
                                      : p
                                  ));
                                }}
                                disabled={(parcel.received_quantity || 0) === parcel.loaded_quantity}
                              >
                                Receive All
                              </Button>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => {
                                  // Decrease received quantity
                                  setParcelsToReceive(prev => prev.map(p =>
                                    p.lr_number === parcel.lr_number
                                      ? { ...p, received_quantity: Math.max(0, (p.received_quantity || 0) - 1) }
                                      : p
                                  ));
                                }}
                                disabled={(parcel.received_quantity || 0) === 0}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="text-lg font-medium w-10 text-center">
                                {parcel.received_quantity || 0}
                              </span>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => {
                                  // Increase received quantity
                                  setParcelsToReceive(prev => prev.map(p =>
                                    p.lr_number === parcel.lr_number
                                      ? { ...p, received_quantity: Math.min(p.loaded_quantity, (p.received_quantity || 0) + 1) }
                                      : p
                                  ));
                                }}
                                disabled={(parcel.received_quantity || 0) === parcel.loaded_quantity}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="flex justify-end mt-4">
                            <Button
                              onClick={() => {
                                // Add to selected parcels
                                const updatedParcel = parcelsToReceive.find(p => p.lr_number === parcel.lr_number);
                                if (updatedParcel && (updatedParcel.received_quantity || 0) > 0) {
                                  setSelectedParcels(prev => [...prev, updatedParcel]);
                                  // Clear the current LR input
                                  setCurrentLR("");
                                  toast({
                                    title: "Parcel Added",
                                    description: `LR ${updatedParcel.lr_number} added to the list`,
                                  });
                                } else {
                                  toast({
                                    title: "Cannot Add Parcel",
                                    description: "Please receive at least one item",
                                    variant: "destructive",
                                  });
                                }
                              }}
                              disabled={(parcel.received_quantity || 0) === 0}
                            >
                              Add to List
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  }
                </div>
              )}

              {/* List of added parcels */}
              {selectedParcels.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Added Parcels</Label>
                    <Badge variant="outline" className="font-normal">
                      {selectedParcels.length} added
                    </Badge>
                  </div>
                  <ScrollArea className="h-[200px] rounded-md border">
                    <div className="p-4 space-y-2">
                      {selectedParcels.map((parcel) => (
                        <div key={parcel.lr_number} className="flex justify-between items-center py-2 border-b last:border-0">
                          <div>
                            <p className="font-medium">{parcel.lr_number}</p>
                            <p className="text-xs text-muted-foreground">
                              From: {parcel.sender_branch?.name} • To: {parcel.delivery_branch?.name}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">
                              Qty: {parcel.received_quantity}/{parcel.loaded_quantity}
                            </Badge>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => {
                                // Remove from selected parcels
                                setSelectedParcels(prev => prev.filter(p => p.lr_number !== parcel.lr_number));
                              }}
                            >
                              <AlertCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              <div className="flex justify-between">
                <Button variant="outline" onClick={handleReset}>
                  Cancel
                </Button>
                {selectedParcels.length > 0 && (
                  <Button onClick={() => setStep("confirmation")}>
                    Proceed to Confirmation
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}



      {step === "confirmation" && memo && (
        <Card className="w-full max-w-2xl mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Confirm Receipt</h2>
              <p className="text-muted-foreground">
                Review and confirm the parcels you are receiving
              </p>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2">Receipt Details:</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm">
                      <span className="font-medium">Vehicle:</span> {memo.vehicle.registration_number} ({memo.vehicle.vehicle_type})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Memo:</span> {memo.memo_number}
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Date:</span> {new Date().toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm">
                      <span className="font-medium">From:</span> {memo.from_branch.name} ({memo.from_branch.code})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">To:</span> {memo.to_branch.name} ({memo.to_branch.code})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Total Parcels:</span> {selectedParcels.length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Parcels Being Received</Label>
                  <Badge variant="outline" className="font-normal">
                    {selectedParcels.length} parcels
                  </Badge>
                </div>
                <ScrollArea className="h-[300px] rounded-md border">
                  <div className="p-4 space-y-4">
                    {selectedParcels.map((parcel) => (
                      <Card key={parcel.lr_number} className="p-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center">
                              <span className="font-medium">{parcel.lr_number}</span>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              From: {parcel.sender_branch?.name || 'Unknown'} •
                              To: {parcel.delivery_branch?.name || 'Unknown'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Sender: {parcel.sender_name} •
                              Recipient: {parcel.recipient_name}
                            </p>
                          </div>
                          <Badge variant="outline">
                            {parcel.received_quantity} of {parcel.loaded_quantity} items
                          </Badge>
                        </div>

                        <div className="mt-2 pt-2 border-t">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm">Items Received:</span>
                              <span className="text-sm font-medium">
                                {parcel.received_quantity} of {parcel.loaded_quantity}
                              </span>
                            </div>

                            <div className="h-2 bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full bg-primary"
                                style={{
                                  width: `${(parcel.received_quantity / parcel.loaded_quantity) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}

                    {selectedParcels.length === 0 && (
                      <div className="text-center text-muted-foreground py-8">
                        No parcels added yet. Go back to add parcels.
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep("lr")}>
                  Back to Add More
                </Button>
                <Button
                  onClick={handleSubmitReceived}
                  disabled={isSubmitting || selectedParcels.length === 0}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Confirm Receipt"
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {step === "complete" && receiptNumber && (
        <Card className="w-full max-w-2xl mx-auto p-6 shadow-lg border-2">
          <CardContent className="space-y-6 p-0">
            <div className="text-center space-y-2">
              <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold tracking-tight">Receipt Complete</h2>
              <p className="text-muted-foreground">
                Parcels have been successfully received
              </p>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-md">
                <h3 className="font-medium mb-2 text-center">Receipt #{receiptNumber}</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm">
                      <span className="font-medium">Vehicle:</span> {memo?.vehicle.registration_number}
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Memo:</span> {memo?.memo_number}
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Date:</span> {new Date().toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm">
                      <span className="font-medium">From:</span> {memo?.from_branch.name} ({memo?.from_branch.code})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">To:</span> {memo?.to_branch.name} ({memo?.to_branch.code})
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Parcels Received:</span> {selectedParcels.length}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Received Parcels</Label>
                  <Badge variant="outline" className="font-normal">
                    {selectedParcels.length} parcels
                  </Badge>
                </div>
                <ScrollArea className="h-[300px] rounded-md border">
                  <div className="p-4 space-y-4">
                    {selectedParcels.map((parcel) => (
                      <Card key={parcel.lr_number} className="p-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center">
                              <span className="font-medium">{parcel.lr_number}</span>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              From: {parcel.sender_branch?.name || 'Unknown'} •
                              To: {parcel.delivery_branch?.name || 'Unknown'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Sender: {parcel.sender_name} •
                              Recipient: {parcel.recipient_name}
                            </p>
                          </div>
                          <Badge variant="outline">
                            {parcel.received_quantity} of {parcel.loaded_quantity} items
                          </Badge>
                        </div>

                        <div className="mt-2 pt-2 border-t">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm">Items Received:</span>
                              <span className="text-sm font-medium">
                                {parcel.received_quantity} of {parcel.loaded_quantity}
                              </span>
                            </div>

                            <div className="h-2 bg-muted rounded-full overflow-hidden">
                              <div
                                className="h-full bg-primary"
                                style={{
                                  width: `${(parcel.received_quantity / parcel.loaded_quantity) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              <div className="flex justify-center space-x-2">
                <Button variant="outline" onClick={() => {
                  // Format the WhatsApp message
                  let message = `Receipt #${receiptNumber}\n` +
                    `Vehicle: ${memo?.vehicle.registration_number || 'N/A'}\n` +
                    `Memo: ${memo?.memo_number || 'N/A'}\n` +
                    `From: ${memo?.from_branch.name || 'N/A'} (${memo?.from_branch.code || 'N/A'})\n` +
                    `To: ${memo?.to_branch.name || 'N/A'} (${memo?.to_branch.code || 'N/A'})\n\n` +
                    `Parcels Received:\n`;

                  selectedParcels.forEach((parcel, index) => {
                    message += `${index + 1}. ${parcel.lr_number} (Qty: ${parcel.received_quantity}/${parcel.loaded_quantity})\n`;
                  });

                  // Encode the message for WhatsApp
                  const encodedMessage = encodeURIComponent(message);
                  window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                }}>
                  <Share className="mr-2 h-4 w-4" />
                  Share Receipt
                </Button>
                <Button onClick={handleReset}>
                  Receive More Parcels
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
