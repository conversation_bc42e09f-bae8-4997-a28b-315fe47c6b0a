"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { VehicleLoadingPanel } from "@/components/vehicle-loading-panel"
import { VehicleReceivingPanel } from "@/components/vehicle-receiving-panel"

export function LoadReceiveManagement() {
  return (
    <div className="space-y-4">
      <Tabs defaultValue="load" className="space-y-4">
        <TabsList>
          <TabsTrigger value="load">Load Parcels</TabsTrigger>
          <TabsTrigger value="receive">Receive Parcels</TabsTrigger>
        </TabsList>
        <TabsContent value="load" className="space-y-4">
          <VehicleLoadingPanel />
        </TabsContent>
        <TabsContent value="receive" className="space-y-4">
          <VehicleReceivingPanel />
        </TabsContent>
      </Tabs>
    </div>
  )
}
